using System.Net.Http.Json;
using System.Text.Json;
using System.Text;

namespace McpClient.Services;

public class EpicAiService : IAiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<EpicAiService> _logger;
    private readonly string _apiKey;
    private readonly string _baseUrl;

    public EpicAiService(HttpClient httpClient, ILogger<EpicAiService> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _apiKey = configuration["EpicAi:ApiKey"] ?? "ecs-00";
        _baseUrl = configuration["EpicAi:BaseUrl"] ?? "https://api.epicai.fun/v1";
        
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
    }

    public async Task<string> GenerateResponseAsync(string userQuestion, string? toolResults = null, List<string>? availableTools = null)
    {
        try
        {
            var systemPrompt = BuildSystemPrompt(availableTools);
            var userPrompt = BuildUserPrompt(userQuestion, toolResults);

            var requestBody = new
            {
                model = "gpt-4.1-nano",
                messages = new[]
                {
                    new { role = "system", content = systemPrompt },
                    new { role = "user", content = userPrompt }
                },
                max_tokens = 500,
                temperature = 0.7,
                stream = false  // Disable streaming to get a regular JSON response
            };

            _logger.LogInformation("Sending request to EpicAI API");

            var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/chat/completions", requestBody);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("EpicAI API error: {StatusCode} - {Content}", response.StatusCode, errorContent);
                return GetFallbackResponse(userQuestion, toolResults);
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            _logger.LogInformation("EpicAI API response: {Response}", responseContent);

            try
            {
                var jsonResponse = JsonDocument.Parse(responseContent);

                var aiResponse = jsonResponse.RootElement
                    .GetProperty("choices")[0]
                    .GetProperty("message")
                    .GetProperty("content")
                    .GetString();

                return aiResponse ?? GetFallbackResponse(userQuestion, toolResults);
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to parse JSON response: {Response}", responseContent);

                // Handle streaming response format
                if (!string.IsNullOrEmpty(responseContent) && responseContent.StartsWith("data:"))
                {
                    return ParseStreamingResponse(responseContent);
                }

                // If the response is plain text, return it directly
                if (!string.IsNullOrEmpty(responseContent) && !responseContent.StartsWith('{'))
                {
                    return responseContent;
                }

                return GetFallbackResponse(userQuestion, toolResults);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling EpicAI API");
            return GetFallbackResponse(userQuestion, toolResults);
        }
    }

    private static string BuildSystemPrompt(List<string>? availableTools)
    {
        var toolsInfo = availableTools?.Any() == true 
            ? $"Available tools: {string.Join(", ", availableTools)}"
            : "No tools available";

        return $"""
            You are a helpful AI assistant that can use Model Context Protocol (MCP) tools to provide accurate information.
            
            {toolsInfo}
            
            When tool results are provided, use them to give accurate, helpful responses. 
            Be conversational and natural in your responses.
            If asked about time/date and you have tool results, use that information.
            Keep responses concise but informative.
            """;
    }

    private static string BuildUserPrompt(string userQuestion, string? toolResults)
    {
        if (string.IsNullOrEmpty(toolResults))
        {
            return userQuestion;
        }

        return $"""
            User question: {userQuestion}
            
            Tool results: {toolResults}
            
            Please provide a helpful response based on the user's question and the tool results.
            """;
    }

    private static string GetFallbackResponse(string userQuestion, string? toolResults)
    {
        if (!string.IsNullOrEmpty(toolResults))
        {
            // If we have tool results, try to extract useful information
            if (toolResults.Contains("Current time"))
            {
                var timeInfo = ExtractTimeFromResponse(toolResults);
                if (userQuestion.Contains("date", StringComparison.OrdinalIgnoreCase) ||
                    userQuestion.Contains("day", StringComparison.OrdinalIgnoreCase))
                {
                    return $"Today is {timeInfo}.";
                }
                if (userQuestion.Contains("time", StringComparison.OrdinalIgnoreCase))
                {
                    return $"The current time is {timeInfo}.";
                }
                return $"The current date and time is {timeInfo}.";
            }
        }

        return "I'm an AI assistant that can help you with various questions. I have access to tools that can provide current time information. How can I help you today?";
    }

    private static string ParseStreamingResponse(string streamingResponse)
    {
        try
        {
            // Extract content from streaming response
            var lines = streamingResponse.Split('\n');
            var contentBuilder = new StringBuilder();

            foreach (var line in lines)
            {
                if (line.StartsWith("data: ") && !line.Contains("[DONE]"))
                {
                    var jsonData = line.Substring(6); // Remove "data: " prefix
                    if (!string.IsNullOrEmpty(jsonData) && jsonData.StartsWith('{'))
                    {
                        try
                        {
                            var chunk = JsonDocument.Parse(jsonData);
                            var content = chunk.RootElement
                                .GetProperty("choices")[0]
                                .GetProperty("delta")
                                .GetProperty("content")
                                .GetString();

                            if (!string.IsNullOrEmpty(content))
                            {
                                contentBuilder.Append(content);
                            }
                        }
                        catch (JsonException)
                        {
                            // Skip invalid JSON chunks
                            continue;
                        }
                    }
                }
            }

            return contentBuilder.ToString();
        }
        catch (Exception)
        {
            // If parsing fails, return the raw response
            return streamingResponse;
        }
    }

    private static string ExtractTimeFromResponse(string timeInfo)
    {
        if (timeInfo.Contains("Current time (local):"))
        {
            var prefix = "Current time (local):";
            var timeStart = timeInfo.IndexOf(prefix) + prefix.Length;
            var extractedTime = timeInfo[timeStart..].Trim();
            extractedTime = extractedTime.Replace("\u202F", " ");
            return extractedTime;
        }
        return timeInfo;
    }
}
