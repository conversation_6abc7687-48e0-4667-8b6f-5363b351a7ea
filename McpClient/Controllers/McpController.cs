using Microsoft.AspNetCore.Mvc;
using McpClient.Models;
using McpClient.Services;

namespace McpClient.Controllers;

[ApiController]
[Route("api/[controller]")]
public class McpController : ControllerBase
{
    private readonly IMcpClientService _mcpClientService;
    private readonly ILogger<McpController> _logger;

    public McpController(IMcpClientService mcpClientService, ILogger<McpController> logger)
    {
        _mcpClientService = mcpClientService;
        _logger = logger;
    }

    /// <summary>
    /// Ask a question to the MCP server and get an intelligent response
    /// </summary>
    /// <param name="request">The question and optional server configuration</param>
    /// <returns>An intelligent response using available MCP tools</returns>
    [HttpPost("ask")]
    public async Task<ActionResult<AskResponse>> Ask([FromBody] AskRequest request)
    {
        if (string.IsNullOrWhiteSpace(request.Question))
        {
            return BadRequest(new AskResponse
            {
                Success = false,
                Error = "Question cannot be empty",
                Answer = "Please provide a valid question."
            });
        }

        _logger.LogInformation("Processing question: {Question}", request.Question);

        try
        {
            var response = await _mcpClientService.AskQuestionAsync(request);
            
            if (response.Success)
            {
                _logger.LogInformation("Successfully processed question with tools: {Tools}", 
                    string.Join(", ", response.ToolsUsed ?? new List<string>()));
            }
            else
            {
                _logger.LogWarning("Failed to process question: {Error}", response.Error);
            }

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error processing question");
            return StatusCode(500, new AskResponse
            {
                Success = false,
                Error = "Internal server error",
                Answer = "An unexpected error occurred while processing your question."
            });
        }
    }

    /// <summary>
    /// Get information about connected MCP servers
    /// </summary>
    /// <returns>List of connected servers and their capabilities</returns>
    [HttpGet("servers")]
    public async Task<ActionResult<List<ServerInfo>>> GetServers()
    {
        try
        {
            var servers = await _mcpClientService.GetConnectedServersAsync();
            return Ok(servers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving server information");
            return StatusCode(500, "Failed to retrieve server information");
        }
    }

    /// <summary>
    /// Connect to a new MCP server
    /// </summary>
    /// <param name="serverPath">Path to the MCP server script</param>
    /// <returns>Success status</returns>
    [HttpPost("servers/connect")]
    public async Task<ActionResult<bool>> ConnectToServer([FromBody] string serverPath)
    {
        if (string.IsNullOrWhiteSpace(serverPath))
        {
            return BadRequest("Server path cannot be empty");
        }

        try
        {
            var success = await _mcpClientService.ConnectToServerAsync(serverPath);
            if (success)
            {
                _logger.LogInformation("Successfully connected to server: {ServerPath}", serverPath);
                return Ok(true);
            }
            else
            {
                _logger.LogWarning("Failed to connect to server: {ServerPath}", serverPath);
                return BadRequest("Failed to connect to server");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error connecting to server: {ServerPath}", serverPath);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Disconnect from an MCP server
    /// </summary>
    /// <param name="serverPath">Path to the MCP server script</param>
    /// <returns>Success status</returns>
    [HttpPost("servers/disconnect")]
    public async Task<ActionResult<bool>> DisconnectFromServer([FromBody] string serverPath)
    {
        if (string.IsNullOrWhiteSpace(serverPath))
        {
            return BadRequest("Server path cannot be empty");
        }

        try
        {
            var success = await _mcpClientService.DisconnectFromServerAsync(serverPath);
            return Ok(success);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disconnecting from server: {ServerPath}", serverPath);
            return StatusCode(500, "Internal server error");
        }
    }

    /// <summary>
    /// Get available tools from a specific MCP server
    /// </summary>
    /// <param name="serverPath">Path to the MCP server script</param>
    /// <returns>List of available tools</returns>
    [HttpGet("servers/{serverPath}/tools")]
    public async Task<ActionResult<List<string>>> GetAvailableTools(string serverPath)
    {
        if (string.IsNullOrWhiteSpace(serverPath))
        {
            return BadRequest("Server path cannot be empty");
        }

        try
        {
            var tools = await _mcpClientService.GetAvailableToolsAsync(serverPath);
            return Ok(tools);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving tools from server: {ServerPath}", serverPath);
            return StatusCode(500, "Failed to retrieve tools");
        }
    }
}
