using McpClientDotNet.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add HTTP clients
builder.Services.AddHttpClient<IMcpServerService, McpServerService>();
builder.Services.AddHttpClient<IGptService, GptService>();

// Add custom services
builder.Services.AddSingleton<IChatSessionService, ChatSessionService>();
builder.Services.AddScoped<IQuestionEnhancementService, QuestionEnhancementService>();
builder.Services.AddScoped<IToolExecutionService, ToolExecutionService>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Enable CORS
app.UseCors();

// Enable static files
app.UseStaticFiles();

// Enable routing
app.UseRouting();

// Map controllers
app.MapControllers();

// Serve index.html for root requests
app.MapFallbackToFile("index.html");

app.Run();
