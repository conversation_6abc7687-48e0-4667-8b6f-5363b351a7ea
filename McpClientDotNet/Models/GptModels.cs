using System.Text.Json.Serialization;

namespace McpClientDotNet.Models;

public class GptRequest
{
    [JsonPropertyName("model")]
    public string Model { get; set; } = "gpt-4.1-nano";

    [JsonPropertyName("messages")]
    public List<GptMessage> Messages { get; set; } = new();

    [JsonPropertyName("temperature")]
    public double Temperature { get; set; } = 0.1;

    [JsonPropertyName("stream")]
    public bool Stream { get; set; } = false;
}

public class GptMessage
{
    [JsonPropertyName("role")]
    public string Role { get; set; } = string.Empty;

    [JsonPropertyName("content")]
    public string Content { get; set; } = string.Empty;
}

public class GptResponse
{
    [JsonPropertyName("choices")]
    public List<GptChoice> Choices { get; set; } = new();
}

public class GptChoice
{
    [JsonPropertyName("message")]
    public GptMessage? Message { get; set; }

    [JsonPropertyName("delta")]
    public GptDelta? Delta { get; set; }
}

public class GptDelta
{
    [JsonPropertyName("content")]
    public string? Content { get; set; }
}

public class HealthResponse
{
    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("mcpServerUrl")]
    public string McpServerUrl { get; set; } = string.Empty;

    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
