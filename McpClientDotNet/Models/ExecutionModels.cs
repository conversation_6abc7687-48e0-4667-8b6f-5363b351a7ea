using System.Text.Json.Serialization;

namespace McpClientDotNet.Models;

public class ExecutionStep
{
    [JsonPropertyName("step")]
    public int Step { get; set; }

    [JsonPropertyName("reasoning")]
    public string Reasoning { get; set; } = string.Empty;

    [JsonPropertyName("tools")]
    public List<ToolCall> Tools { get; set; } = new();

    [JsonPropertyName("results")]
    public List<ToolResult> Results { get; set; } = new();

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
}

public class MissingParameter
{
    [JsonPropertyName("toolName")]
    public string? ToolName { get; set; }

    [JsonPropertyName("param")]
    public string Param { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string Description { get; set; } = string.Empty;
}

public class PlanningDecision
{
    [JsonPropertyName("action")]
    public string Action { get; set; } = string.Empty;

    [JsonPropertyName("answer")]
    public string? Answer { get; set; }

    [JsonPropertyName("tools")]
    public List<ToolCall> Tools { get; set; } = new();

    [JsonPropertyName("reasoning")]
    public string? Reasoning { get; set; }

    [JsonPropertyName("question")]
    public string? Question { get; set; }

    [JsonPropertyName("missingParams")]
    public List<MissingParameter> MissingParams { get; set; } = new();

    [JsonPropertyName("reason")]
    public string? Reason { get; set; }
}

public class MultiStepResult
{
    public bool Success { get; set; }
    public string? Answer { get; set; }
    public List<ExecutionStep> ExecutionLog { get; set; } = new();
    public int TotalSteps { get; set; }
    public string? ChatId { get; set; }
    public bool NeedsUserInput { get; set; }
    public string? Question { get; set; }
    public List<MissingParameter> MissingParams { get; set; } = new();
    public string? Reason { get; set; }
}
