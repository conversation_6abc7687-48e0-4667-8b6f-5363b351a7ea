using System.Text.Json.Serialization;

namespace McpClientDotNet.Models;

public class AskResponse
{
    [JsonPropertyName("chatId")]
    public string? ChatId { get; set; }

    [JsonPropertyName("question")]
    public string? Question { get; set; }

    [JsonPropertyName("response")]
    public string? Response { get; set; }

    [JsonPropertyName("needsUserInput")]
    public bool NeedsUserInput { get; set; }

    [JsonPropertyName("userQuestion")]
    public string? UserQuestion { get; set; }

    [JsonPropertyName("missingParams")]
    public List<MissingParameter> MissingParams { get; set; } = new();

    [JsonPropertyName("executionLog")]
    public List<ExecutionStep> ExecutionLog { get; set; } = new();

    [JsonPropertyName("totalSteps")]
    public int TotalSteps { get; set; }

    [JsonPropertyName("approach")]
    public string? Approach { get; set; }

    [JsonPropertyName("toolsUsed")]
    public List<ToolCall> ToolsUsed { get; set; } = new();

    [JsonPropertyName("toolResults")]
    public List<ToolResult> ToolResults { get; set; } = new();

    [JsonPropertyName("error")]
    public string? Error { get; set; }
}
