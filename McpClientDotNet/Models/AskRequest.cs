using System.Text.Json.Serialization;

namespace McpClientDotNet.Models;

public class AskRequest
{
    [JsonPropertyName("question")]
    public string Question { get; set; } = string.Empty;

    [JsonPropertyName("useMultiStep")]
    public bool UseMultiStep { get; set; } = true;

    [JsonPropertyName("chatId")]
    public string? ChatId { get; set; }

    [JsonPropertyName("userContext")]
    public Dictionary<string, object> UserContext { get; set; } = new();
}
