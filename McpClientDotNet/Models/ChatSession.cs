using System.Text.Json.Serialization;

namespace McpClientDotNet.Models;

public class ChatSession
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("messages")]
    public List<ChatMessage> Messages { get; set; } = new();

    [JsonPropertyName("context")]
    public Dictionary<string, object> Context { get; set; } = new();

    [JsonPropertyName("created")]
    public DateTime Created { get; set; } = DateTime.UtcNow;
}

public class ChatMessage
{
    [JsonPropertyName("role")]
    public string Role { get; set; } = string.Empty;

    [JsonPropertyName("content")]
    public string Content { get; set; } = string.Empty;

    [JsonPropertyName("timestamp")]
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
