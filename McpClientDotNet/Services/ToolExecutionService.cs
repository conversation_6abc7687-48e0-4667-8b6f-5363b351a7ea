using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using McpClientDotNet.Models;

namespace McpClientDotNet.Services;

public class ToolExecutionService : IToolExecutionService
{
    private readonly IGptService _gptService;
    private readonly IMcpServerService _mcpServerService;
    private readonly IQuestionEnhancementService _questionEnhancementService;
    private readonly ILogger<ToolExecutionService> _logger;

    public ToolExecutionService(
        IGptService gptService,
        IMcpServerService mcpServerService,
        IQuestionEnhancementService questionEnhancementService,
        ILogger<ToolExecutionService> logger)
    {
        _gptService = gptService;
        _mcpServerService = mcpServerService;
        _questionEnhancementService = questionEnhancementService;
        _logger = logger;
    }

    public async Task<MultiStepResult> ExecuteMultiStepPlanAsync(string question, List<Tool> tools, int maxSteps, string? chatId, Dictionary<string, object> userContext)
    {
        var executionLog = new List<ExecutionStep>();
        var enhancedQuestion = _questionEnhancementService.EnhanceVaultQuestion(question, userContext);
        var currentContext = $"Original question: \"{enhancedQuestion}\"";
        var stepCount = 0;

        // Include user context if available
        if (userContext.Count > 0)
        {
            currentContext += $"\n\nUser context: {JsonSerializer.Serialize(userContext)}";
        }

        while (stepCount < maxSteps)
        {
            stepCount++;

            // Create planning prompt
            var planningPrompt = CreatePlanningPrompt(currentContext, tools, executionLog);

            var planningResponse = await _gptService.CallGptAsync(new List<GptMessage>
            {
                new() { Role = "user", Content = planningPrompt }
            });

            _logger.LogDebug("Planning response received: {Response}", planningResponse);

            var planningDecision = await ParsePlanningResponseAsync(planningResponse, userContext);

            _logger.LogDebug("Planning decision: {Decision}", JsonSerializer.Serialize(planningDecision));

            if (planningDecision.Action == "complete")
            {
                return new MultiStepResult
                {
                    Success = true,
                    Answer = planningDecision.Answer,
                    ExecutionLog = executionLog,
                    TotalSteps = stepCount,
                    ChatId = chatId
                };
            }

            if (planningDecision.Action == "ask_user")
            {
                return new MultiStepResult
                {
                    Success = false,
                    NeedsUserInput = true,
                    Question = planningDecision.Question,
                    MissingParams = planningDecision.MissingParams,
                    ExecutionLog = executionLog,
                    TotalSteps = stepCount,
                    ChatId = chatId
                };
            }

            if (planningDecision.Action == "impossible")
            {
                return new MultiStepResult
                {
                    Success = false,
                    Reason = planningDecision.Reason,
                    ExecutionLog = executionLog,
                    TotalSteps = stepCount,
                    ChatId = chatId
                };
            }

            if (planningDecision.Action == "execute")
            {
                // Check if tools have missing required parameters
                var missingParams = ValidateToolParameters(planningDecision.Tools, tools);

                if (missingParams.Count > 0)
                {
                    return new MultiStepResult
                    {
                        Success = false,
                        NeedsUserInput = true,
                        Question = "I need some additional information to proceed:",
                        MissingParams = missingParams,
                        ExecutionLog = executionLog,
                        TotalSteps = stepCount,
                        ChatId = chatId
                    };
                }

                // Execute the planned tools
                var stepResults = await ExecuteToolsAsync(planningDecision.Tools);

                // Add to execution log
                executionLog.Add(new ExecutionStep
                {
                    Step = stepCount,
                    Reasoning = planningDecision.Reasoning ?? "Executing tools",
                    Tools = planningDecision.Tools,
                    Results = stepResults,
                    Description = planningDecision.Reasoning ?? "Executing tools"
                });

                // Update context for next iteration
                currentContext = UpdateContextForNextIteration(enhancedQuestion, executionLog);
            }
        }

        // If we reach here, we've hit max steps - try to provide a helpful summary
        return await CreateSummaryResponseAsync(question, executionLog, stepCount, chatId);
    }

    private string CreatePlanningPrompt(string currentContext, List<Tool> tools, List<ExecutionStep> executionLog)
    {
        var toolDescriptions = string.Join("\n", tools.Select(tool =>
        {
            var paramsDesc = string.Join(", ", tool.Params.Select(p =>
                $"{p.Name} ({(p.Required ? "required" : "optional")}): {p.Description}"));
            return $"- {tool.Name}: {tool.Description}{(string.IsNullOrEmpty(paramsDesc) ? "" : $" | Parameters: {paramsDesc}")}";
        }));

        var previousSteps = string.Join("\n\n", executionLog.Select((step, i) =>
            $"Step {i + 1}: {step.Description}\nResult: {JsonSerializer.Serialize(step.Results)}"));

        return $@"You are Vaulty, a specialized AI assistant for Autodesk Vault operations. You excel at helping users manage, search, and analyze their Vault files and data.

{currentContext}

Available tools:
{toolDescriptions}

VAULT-FOCUSED TOOL SELECTION:
- For ANY file-related questions (list, search, find, show, get files), use vaultGetFiles tool
- For date/time questions or calculations, use getTime or date calculation tools
- Always prioritize Vault operations over general file system operations
- If you need credentials for tools (like accessToken, vaultServer, vaultId), use the values from the current context above
- NEVER use placeholder values like ""your_access_token"" or ""https://your-vault-server.com""

VAULT QUERY PATTERNS:
- ""files"" = use vaultGetFiles
- ""list"" = use vaultGetFiles
- ""search"" = use vaultGetFiles with searchTerm
- ""find"" = use vaultGetFiles with searchTerm
- ""show"" = use vaultGetFiles
- ""checked out"" = use vaultGetFiles (results show checkout status)
- ""available"" = use vaultGetFiles (results show availability)
- ""recent"" or ""last X days"" = use vaultGetFiles (results are sorted by date)

Previous execution steps:
{previousSteps}

Analyze the situation and decide what to do next. You MUST respond with valid JSON only, no additional text.

VAULTY'S RESPONSE GUIDELINES:
- For simple greetings, respond as Vaulty with Vault-focused capabilities
- If you have executed tools and retrieved Vault data, provide clear, organized results
- Only use ""ask_user"" when you need Vault credentials or specific search parameters
- Only use ""execute"" when you have all required parameters to run tools
- Present Vault results in a clear, readable format without markdown formatting
- For file lists, organize by type and show key information (version, checkout status)
- Be proactive in suggesting related Vault operations the user might want
- Use plain text with simple formatting like line breaks and basic structure

1. If you have enough information to answer the original question OR if it's a simple greeting/conversation OR if you have data from previous tool executions, respond with:
   {{""action"": ""complete"", ""answer"": ""your final answer here""}}

2. If you need to execute tools and have all required parameters, respond with:
   {{""action"": ""execute"", ""tools"": [{{""toolName"": ""toolName"", ""params"": {{}}, ""description"": ""why you're using this tool""}}], ""reasoning"": ""explain what you're trying to find out""}}

3. If you need information from the user (missing required technical parameters for tools), respond with:
   {{""action"": ""ask_user"", ""question"": ""What specific technical information do you need?"", ""missingParams"": [{{""param"": ""paramName"", ""description"": ""what this parameter is for""}}]}}

4. If the question cannot be answered with available tools, respond with:
   {{""action"": ""impossible"", ""reason"": ""explanation of why it cannot be answered""}}

IMPORTANT: Respond with ONLY the JSON object, no other text or formatting.";
    }

    private async Task<PlanningDecision> ParsePlanningResponseAsync(string planningResponse, Dictionary<string, object> combinedContext)
    {
        try
        {
            // Clean up the response to extract JSON
            var cleanResponse = planningResponse.Trim();

            // Try multiple JSON extraction methods
            string? jsonStr = null;

            // Method 1: Look for complete JSON object
            var jsonMatch = Regex.Match(cleanResponse, @"\{[\s\S]*\}");
            if (jsonMatch.Success)
            {
                jsonStr = jsonMatch.Value;
            }

            // Method 2: If no JSON found, try to extract from code blocks
            if (string.IsNullOrEmpty(jsonStr))
            {
                var codeBlockMatch = Regex.Match(cleanResponse, @"```(?:json)?\s*(\{[\s\S]*?\})\s*```");
                if (codeBlockMatch.Success)
                {
                    jsonStr = codeBlockMatch.Groups[1].Value;
                }
            }

            // Method 3: If still no JSON, check if the entire response is JSON-like
            if (string.IsNullOrEmpty(jsonStr) && cleanResponse.StartsWith('{') && cleanResponse.EndsWith('}'))
            {
                jsonStr = cleanResponse;
            }

            // If we still don't have JSON, create a fallback response
            if (string.IsNullOrEmpty(jsonStr))
            {
                _logger.LogWarning("No JSON found in planning response, creating fallback: {Response}", planningResponse);

                // For simple greetings or questions that don't need tools, just return the response
                if (!string.IsNullOrWhiteSpace(planningResponse))
                {
                    return new PlanningDecision
                    {
                        Action = "complete",
                        Answer = planningResponse.Trim()
                    };
                }
                else
                {
                    return new PlanningDecision
                    {
                        Action = "complete",
                        Answer = "Hello! I'm here to help you with files, Vault data, and other questions. What would you like to know?"
                    };
                }
            }

            var planningDecision = JsonSerializer.Deserialize<PlanningDecision>(jsonStr, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return planningDecision ?? new PlanningDecision
            {
                Action = "complete",
                Answer = "I apologize, but I encountered an issue processing your request. Could you please try rephrasing your question?"
            };
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Planning response parsing failed: {Response}", planningResponse);

            // Try to fix common JSON syntax errors
            var jsonMatch = Regex.Match(planningResponse, @"\{[\s\S]*\}");
            if (jsonMatch.Success)
            {
                try
                {
                    var fixedJson = FixJsonSyntax(jsonMatch.Value, combinedContext);
                    _logger.LogDebug("Attempting to parse fixed JSON: {Json}", fixedJson);

                    var planningDecision = JsonSerializer.Deserialize<PlanningDecision>(fixedJson, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    // Add missing reasoning field if not present
                    if (planningDecision != null && planningDecision.Action == "execute" && string.IsNullOrEmpty(planningDecision.Reasoning))
                    {
                        planningDecision.Reasoning = "Executing tools to fulfill the user's request";
                    }

                    return planningDecision ?? CreateFallbackDecision();
                }
                catch (JsonException fixError)
                {
                    _logger.LogError(fixError, "JSON fix attempt failed");
                    return CreateFallbackDecision();
                }
            }
            else if (!string.IsNullOrWhiteSpace(planningResponse) && !planningResponse.Contains("\"action\""))
            {
                return new PlanningDecision
                {
                    Action = "complete",
                    Answer = planningResponse.Trim()
                };
            }
            else
            {
                return CreateFallbackDecision();
            }
        }
    }

    private static PlanningDecision CreateFallbackDecision()
    {
        return new PlanningDecision
        {
            Action = "complete",
            Answer = "I apologize, but I encountered an issue processing your request. Could you please try rephrasing your question?"
        };
    }

    private string FixJsonSyntax(string json, Dictionary<string, object> combinedContext)
    {
        var fixedJson = json
            .Replace(",}", "}") // Remove trailing commas before closing braces
            .Replace(",]", "]"); // Remove trailing commas before closing brackets

        // Replace placeholder values with actual context values
        if (combinedContext.TryGetValue("accessToken", out var accessToken))
        {
            fixedJson = fixedJson.Replace("\"your_access_token\"", $"\"{accessToken}\"");
        }
        if (combinedContext.TryGetValue("vaultServer", out var vaultServer))
        {
            fixedJson = fixedJson.Replace("\"https://your-vault-server.com\"", $"\"{vaultServer}\"");
        }
        if (combinedContext.TryGetValue("vaultId", out var vaultId))
        {
            fixedJson = fixedJson.Replace("\"your_vault_id\"", $"\"{vaultId}\"");
        }

        return fixedJson;
    }

    private List<MissingParameter> ValidateToolParameters(List<ToolCall> toolCalls, List<Tool> tools)
    {
        var missingParams = new List<MissingParameter>();

        foreach (var toolCall in toolCalls)
        {
            var tool = tools.FirstOrDefault(t => t.Name == toolCall.ToolName);
            if (tool != null)
            {
                var requiredParams = tool.Params.Where(p => p.Required);
                foreach (var param in requiredParams)
                {
                    if (!toolCall.Params.ContainsKey(param.Name) || toolCall.Params[param.Name] == null)
                    {
                        missingParams.Add(new MissingParameter
                        {
                            ToolName = toolCall.ToolName,
                            Param = param.Name,
                            Description = param.Description
                        });
                    }
                }
            }
        }

        return missingParams;
    }

    private async Task<List<ToolResult>> ExecuteToolsAsync(List<ToolCall> toolCalls)
    {
        var stepResults = new List<ToolResult>();

        foreach (var toolCall in toolCalls)
        {
            try
            {
                _logger.LogDebug("Executing tool: {ToolName} with params: {Params}",
                    toolCall.ToolName, JsonSerializer.Serialize(toolCall.Params));

                var result = await _mcpServerService.ExecuteToolOnServerAsync(toolCall.ToolName, toolCall.Params);

                _logger.LogDebug("Tool result: {Result}", JsonSerializer.Serialize(result));

                stepResults.Add(new ToolResult
                {
                    ToolName = toolCall.ToolName,
                    Params = toolCall.Params,
                    Result = result.Result,
                    Description = toolCall.Description
                });
            }
            catch (Exception toolError)
            {
                _logger.LogError(toolError, "Tool execution error for {ToolName}", toolCall.ToolName);
                stepResults.Add(new ToolResult
                {
                    ToolName = toolCall.ToolName,
                    Params = toolCall.Params,
                    Error = toolError.Message,
                    Description = toolCall.Description
                });
            }
        }

        return stepResults;
    }

    private string UpdateContextForNextIteration(string enhancedQuestion, List<ExecutionStep> executionLog)
    {
        var currentFindings = string.Join("\n", executionLog.Select(step =>
            $"- {step.Reasoning}: {string.Join(", ", step.Results.Select(r =>
                r.Error != null ? $"Error: {r.Error}" : JsonSerializer.Serialize(r.Result)))}"));

        return $@"Original question: ""{enhancedQuestion}""

Current findings:
{currentFindings}

IMPORTANT: If you have retrieved data that answers the user's question (like file lists, search results, etc.), you should use ""complete"" action and provide a formatted answer to the user. Do not ask for further clarification when you have the requested data.";
    }

    private async Task<MultiStepResult> CreateSummaryResponseAsync(string question, List<ExecutionStep> executionLog, int stepCount, string? chatId)
    {
        var executionSummary = string.Join("\n", executionLog.Select(step =>
            $"- {step.Reasoning}: {string.Join(", ", step.Results.Select(r =>
                r.Error != null ? $"Error: {r.Error}" : JsonSerializer.Serialize(r.Result)))}"));

        var summaryPrompt = $@"Based on the execution steps below, provide a helpful summary of what was found:

Original question: ""{question}""

Execution steps:
{executionSummary}

Please provide a clear, helpful response based on the information gathered:";

        try
        {
            var summaryResponse = await _gptService.CallGptAsync(new List<GptMessage>
            {
                new() { Role = "user", Content = summaryPrompt }
            });

            return new MultiStepResult
            {
                Success = true,
                Answer = summaryResponse,
                ExecutionLog = executionLog,
                TotalSteps = stepCount,
                ChatId = chatId
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create summary response");
            return new MultiStepResult
            {
                Success = false,
                Reason = $"Reached maximum steps ({stepCount}) without completing the task",
                ExecutionLog = executionLog,
                TotalSteps = stepCount,
                ChatId = chatId
            };
        }
    }

    public string CreateToolSelectionPrompt(string question, List<Tool> tools)
    {
        var toolDescriptions = string.Join("\n", tools.Select(tool =>
        {
            var paramsDesc = string.Join(", ", tool.Params.Select(p =>
                $"{p.Name} ({(p.Required ? "required" : "optional")}): {p.Description}"));
            return $"- {tool.Name}: {tool.Description}{(string.IsNullOrEmpty(paramsDesc) ? "" : $" | Parameters: {paramsDesc}")}";
        }));

        return $@"You are a tool selection assistant. Given a user question and available tools, determine which tool(s) to use and what parameters to provide.

Available tools:
{toolDescriptions}

User question: ""{question}""

Respond with a JSON array of tools to use. Each tool should have:
- toolName: the exact name of the tool
- params: object with parameter values (only include parameters that are needed)

If multiple tools are needed, include them all in the array. If no tools are needed, return an empty array.

Example response format:
[
  {{
    ""toolName"": ""getTime"",
    ""params"": {{}}
  }},
  {{
    ""toolName"": ""vaultGetFiles"",
    ""params"": {{
      ""accessToken"": ""your_token"",
      ""vaultServer"": ""http://localhost:4000"",
      ""vaultId"": ""117""
    }}
  }}
]

Response:";
    }

    public string CreateResponsePrompt(string question, List<ToolResult> toolResults)
    {
        var resultsText = string.Join("\n\n", toolResults.Select(result =>
            $"Tool: {result.ToolName}\nParameters: {JsonSerializer.Serialize(result.Params)}\nResult: {JsonSerializer.Serialize(result.Result)}"));

        return $@"You are a helpful assistant. A user asked a question and we executed some tools to gather information. Please provide a natural, helpful response based on the tool results.

User question: ""{question}""

Tool execution results:
{resultsText}

Please provide a clear, natural response that answers the user's question based on the tool results:";
    }
}
