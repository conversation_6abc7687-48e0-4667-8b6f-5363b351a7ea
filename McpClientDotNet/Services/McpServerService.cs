using System.Text;
using System.Text.Json;
using McpClientDotNet.Models;

namespace McpClientDotNet.Services;

public class McpServerService : IMcpServerService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<McpServerService> _logger;
    private readonly string _mcpServerUrl;

    public McpServerService(HttpClient httpClient, ILogger<McpServerService> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _mcpServerUrl = configuration["McpServerUrl"] ?? "http://localhost:3001";
    }

    public async Task<List<Tool>> FetchAvailableToolsAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_mcpServerUrl}/tools");
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync();
            var toolsResponse = JsonSerializer.Deserialize<ToolsResponse>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return toolsResponse?.Tools ?? new List<Tool>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to fetch tools from MCP server");
            throw new InvalidOperationException($"Failed to fetch tools from MCP server: {ex.Message}", ex);
        }
    }

    public async Task<ToolExecutionResponse> ExecuteToolOnServerAsync(string toolName, Dictionary<string, object> parameters)
    {
        try
        {
            var request = new ToolExecutionRequest
            {
                ToolName = toolName,
                Params = parameters
            };

            var json = JsonSerializer.Serialize(request, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync($"{_mcpServerUrl}/tool", content);

            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                var errorResponse = JsonSerializer.Deserialize<ToolExecutionResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                throw new InvalidOperationException($"Tool execution failed: {errorResponse?.Error ?? "Unknown error"}");
            }

            var result = JsonSerializer.Deserialize<ToolExecutionResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            return result ?? new ToolExecutionResponse { Error = "Invalid response from server" };
        }
        catch (Exception ex) when (!(ex is InvalidOperationException))
        {
            _logger.LogError(ex, "Failed to execute tool {ToolName} on MCP server", toolName);
            throw new InvalidOperationException($"Failed to execute tool: {ex.Message}", ex);
        }
    }
}
