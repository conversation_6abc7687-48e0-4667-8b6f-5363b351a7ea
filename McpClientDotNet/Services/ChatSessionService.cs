using System.Collections.Concurrent;
using McpClientDotNet.Models;

namespace McpClientDotNet.Services;

public class ChatSessionService : IChatSessionService
{
    private readonly ConcurrentDictionary<string, ChatSession> _chatSessions = new();
    private readonly ILogger<ChatSessionService> _logger;

    public ChatSessionService(ILogger<ChatSessionService> logger)
    {
        _logger = logger;
    }

    public ChatSession GetOrCreateSession(string? chatId)
    {
        var sessionId = chatId ?? Guid.NewGuid().ToString();

        return _chatSessions.GetOrAdd(sessionId, id => new ChatSession
        {
            Id = id,
            Messages = new List<ChatMessage>(),
            Context = new Dictionary<string, object>(),
            Created = DateTime.UtcNow
        });
    }

    public ChatSession? GetSession(string chatId)
    {
        _chatSessions.TryGetValue(chatId, out var session);
        return session;
    }

    public void AddMessage(string sessionId, ChatMessage message)
    {
        if (_chatSessions.TryGetValue(sessionId, out var session))
        {
            session.Messages.Add(message);
            _logger.LogDebug("Added message to session {SessionId}: {Role} - {Content}", 
                sessionId, message.Role, message.Content);
        }
        else
        {
            _logger.LogWarning("Attempted to add message to non-existent session {SessionId}", sessionId);
        }
    }

    public void UpdateContext(string sessionId, Dictionary<string, object> context)
    {
        if (_chatSessions.TryGetValue(sessionId, out var session))
        {
            foreach (var kvp in context)
            {
                session.Context[kvp.Key] = kvp.Value;
            }
            _logger.LogDebug("Updated context for session {SessionId}", sessionId);
        }
        else
        {
            _logger.LogWarning("Attempted to update context for non-existent session {SessionId}", sessionId);
        }
    }
}
