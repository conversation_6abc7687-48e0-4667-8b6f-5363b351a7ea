using System.Text;
using System.Text.Json;
using McpClientDotNet.Models;

namespace McpClientDotNet.Services;

public class GptService : IGptService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<GptService> _logger;
    private readonly string _gptApiUrl;
    private readonly string _gptApiKey;

    public GptService(HttpClient httpClient, ILogger<GptService> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _gptApiUrl = configuration["GptApiUrl"] ?? "https://api.epicai.fun/v1/chat/completions";
        _gptApiKey = configuration["GptApiKey"] ?? "ecs-00";
    }

    public async Task<string> CallGptAsync(List<GptMessage> messages)
    {
        try
        {
            var request = new GptRequest
            {
                Model = "gpt-4.1-nano",
                Messages = messages,
                Temperature = 0.1,
                Stream = false
            };

            var json = JsonSerializer.Serialize(request, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_gptApiKey}");

            var response = await _httpClient.PostAsync(_gptApiUrl, content);
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("GPT API Error Response: {Response}", responseContent);
                throw new InvalidOperationException($"GPT API call failed: {response.StatusCode} - {responseContent}");
            }

            // Handle both streaming and non-streaming responses
            if (responseContent.Contains("data: "))
            {
                // This is a streaming response
                return ParseStreamingResponse(responseContent);
            }
            else
            {
                // This is a regular JSON response
                var gptResponse = JsonSerializer.Deserialize<GptResponse>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (gptResponse?.Choices?.Count > 0 && gptResponse.Choices[0].Message != null)
                {
                    return gptResponse.Choices[0].Message.Content;
                }
                else
                {
                    throw new InvalidOperationException($"Invalid GPT API response structure: {responseContent}");
                }
            }
        }
        catch (Exception ex) when (!(ex is InvalidOperationException))
        {
            _logger.LogError(ex, "GPT API call failed");
            throw new InvalidOperationException($"GPT API call failed: {ex.Message}", ex);
        }
    }

    private string ParseStreamingResponse(string streamData)
    {
        var lines = streamData.Split('\n');
        var content = new StringBuilder();

        foreach (var line in lines)
        {
            if (line.StartsWith("data: ") && !line.Contains("[DONE]"))
            {
                try
                {
                    var jsonStr = line.Substring(6);
                    var data = JsonSerializer.Deserialize<GptResponse>(jsonStr, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (data?.Choices?.Count > 0 && 
                        data.Choices[0].Delta?.Content != null)
                    {
                        content.Append(data.Choices[0].Delta.Content);
                    }
                }
                catch (JsonException)
                {
                    // Skip invalid JSON lines
                }
            }
        }

        return content.ToString();
    }
}
