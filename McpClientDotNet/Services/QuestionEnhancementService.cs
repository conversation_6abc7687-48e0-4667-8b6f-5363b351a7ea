using System.Text.RegularExpressions;

namespace McpClientDotNet.Services;

public class QuestionEnhancementService : IQuestionEnhancementService
{
    private readonly ILogger<QuestionEnhancementService> _logger;
    private static readonly Regex SimpleGreetingRegex = new(
        @"^(hi|hello|hey|good morning|good afternoon|good evening|how are you|what's up|sup)[\s\?\!]*$",
        RegexOptions.IgnoreCase | RegexOptions.Compiled);

    public QuestionEnhancementService(ILogger<QuestionEnhancementService> logger)
    {
        _logger = logger;
    }

    public string EnhanceVaultQuestion(string question, Dictionary<string, object> context)
    {
        var lowerQuestion = question.ToLowerInvariant();

        // Add context hints for common Vault queries
        if (lowerQuestion.Contains("files") && !lowerQuestion.Contains("vault"))
        {
            return question + " (from my Vault)";
        }

        if (lowerQuestion.Contains("checked out") || lowerQuestion.Contains("locked"))
        {
            return question + " (show checkout status and user)";
        }

        if (lowerQuestion.Contains("recent") || lowerQuestion.Contains("latest") || lowerQuestion.Contains("new"))
        {
            return question + " (sorted by modification date)";
        }

        if (lowerQuestion.Contains("cad") || lowerQuestion.Contains("drawing"))
        {
            return question + " (include .dwg, .ipt, .iam, .idw files)";
        }

        if (lowerQuestion.Contains("search") || lowerQuestion.Contains("find"))
        {
            return question + " (use searchTerm parameter)";
        }

        return question;
    }

    public bool IsSimpleGreeting(string question)
    {
        return SimpleGreetingRegex.IsMatch(question.Trim());
    }

    public string GetGreetingResponse()
    {
        return "Hello! I'm Vaulty, your specialized Autodesk Vault assistant. I can help you with:\n\n" +
               "• Listing and searching your Vault files\n" +
               "• Checking file status and versions\n" +
               "• Finding checked out files\n" +
               "• Filtering files by type or date\n" +
               "• Managing your Vault data\n\n" +
               "What would you like to explore in your Vault today?";
    }
}
