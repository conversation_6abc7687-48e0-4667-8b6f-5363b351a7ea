using McpClientDotNet.Models;

namespace McpClientDotNet.Services;

public interface IToolExecutionService
{
    Task<MultiStepResult> ExecuteMultiStepPlanAsync(string question, List<Tool> tools, int maxSteps, string? chatId, Dictionary<string, object> userContext);
    string CreateToolSelectionPrompt(string question, List<Tool> tools);
    string CreateResponsePrompt(string question, List<ToolResult> toolResults);
}
