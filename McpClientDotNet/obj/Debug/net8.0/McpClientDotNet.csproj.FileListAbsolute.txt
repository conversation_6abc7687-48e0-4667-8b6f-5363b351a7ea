/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClientDotNet.csproj.AssemblyReference.cache
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClientDotNet.GeneratedMSBuildEditorConfig.editorconfig
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClientDotNet.AssemblyInfoInputs.cache
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClientDotNet.AssemblyInfo.cs
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClientDotNet.csproj.CoreCompileInputs.cache
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClientDotNet.MvcApplicationPartsAssemblyInfo.cs
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClientDotNet.MvcApplicationPartsAssemblyInfo.cache
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/appsettings.Development.json
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/appsettings.json
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/McpClientDotNet.staticwebassets.runtime.json
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/McpClientDotNet
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/McpClientDotNet.deps.json
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/McpClientDotNet.runtimeconfig.json
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/McpClientDotNet.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/McpClientDotNet.pdb
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.AspNetCore.OpenApi.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Configuration.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Configuration.Binder.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Diagnostics.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Http.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Logging.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Logging.Abstractions.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Options.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.Extensions.Primitives.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Microsoft.OpenApi.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Swashbuckle.AspNetCore.Swagger.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerGen.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/Swashbuckle.AspNetCore.SwaggerUI.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/System.Diagnostics.DiagnosticSource.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/System.IO.Pipelines.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/System.Text.Json.dll
/Users/<USER>/mcp_vault/McpClientDotNet/bin/Debug/net8.0/runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/staticwebassets.build.json
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/staticwebassets.development.json
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/staticwebassets/msbuild.McpClientDotNet.Microsoft.AspNetCore.StaticWebAssets.props
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/staticwebassets/msbuild.build.McpClientDotNet.props
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.McpClientDotNet.props
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.McpClientDotNet.props
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/staticwebassets.pack.json
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/scopedcss/bundle/McpClientDotNet.styles.css
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClien.9DC46611.Up2Date
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClientDotNet.dll
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/refint/McpClientDotNet.dll
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClientDotNet.pdb
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/McpClientDotNet.genruntimeconfig.cache
/Users/<USER>/mcp_vault/McpClientDotNet/obj/Debug/net8.0/ref/McpClientDotNet.dll
