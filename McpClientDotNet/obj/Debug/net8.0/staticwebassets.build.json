{"Version": 1, "Hash": "ZrGw/JZre19NejPN8Gqo16lArlwXRXYp8kZO1oAksIs=", "Source": "McpClientDotNet", "BasePath": "_content/McpClientDotNet", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "McpClientDotNet/wwwroot", "Source": "McpClientDotNet", "ContentRoot": "/Users/<USER>/mcp_vault/McpClientDotNet/wwwroot/", "BasePath": "_content/McpClientDotNet", "Pattern": "**"}], "Assets": [{"Identity": "/Users/<USER>/mcp_vault/McpClientDotNet/wwwroot/index.html", "SourceId": "McpClientDotNet", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/mcp_vault/McpClientDotNet/wwwroot/", "BasePath": "_content/McpClientDotNet", "RelativePath": "index.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/index.html"}]}