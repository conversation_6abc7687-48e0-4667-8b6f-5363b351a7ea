{"version": 2, "dgSpecHash": "1Ui9HbYvtwA=", "success": true, "projectFilePath": "/Users/<USER>/mcp_vault/McpClientDotNet/McpClientDotNet.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/microsoft.aspnetcore.cors/2.3.0/microsoft.aspnetcore.cors.2.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.abstractions/2.3.0/microsoft.aspnetcore.http.abstractions.2.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.extensions/2.3.0/microsoft.aspnetcore.http.extensions.2.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.http.features/2.3.0/microsoft.aspnetcore.http.features.2.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.openapi/8.0.14/microsoft.aspnetcore.openapi.8.0.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.apidescription.server/6.0.5/microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration/9.0.6/microsoft.extensions.configuration.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/9.0.6/microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.configuration.binder/9.0.6/microsoft.extensions.configuration.binder.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/9.0.6/microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/9.0.6/microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics/9.0.6/microsoft.extensions.diagnostics.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.diagnostics.abstractions/9.0.6/microsoft.extensions.diagnostics.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.fileproviders.abstractions/8.0.0/microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.http/9.0.6/microsoft.extensions.http.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging/9.0.6/microsoft.extensions.logging.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/9.0.6/microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options/9.0.6/microsoft.extensions.options.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.options.configurationextensions/9.0.6/microsoft.extensions.options.configurationextensions.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.extensions.primitives/9.0.6/microsoft.extensions.primitives.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.http.headers/2.3.0/microsoft.net.http.headers.2.3.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.openapi/1.6.14/microsoft.openapi.1.6.14.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore/6.6.2/swashbuckle.aspnetcore.6.6.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swagger/6.6.2/swashbuckle.aspnetcore.swagger.6.6.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggergen/6.6.2/swashbuckle.aspnetcore.swaggergen.6.6.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggerui/6.6.2/swashbuckle.aspnetcore.swaggerui.6.6.2.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.buffers/4.6.0/system.buffers.4.6.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/9.0.6/system.diagnostics.diagnosticsource.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.io.pipelines/9.0.6/system.io.pipelines.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.encodings.web/9.0.6/system.text.encodings.web.9.0.6.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.text.json/9.0.6/system.text.json.9.0.6.nupkg.sha512"], "logs": []}