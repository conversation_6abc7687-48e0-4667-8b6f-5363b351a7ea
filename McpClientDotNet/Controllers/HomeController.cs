using Microsoft.AspNetCore.Mvc;
using McpClientDotNet.Models;
using McpClientDotNet.Services;

namespace McpClientDotNet.Controllers;

[ApiController]
[Route("")]
public class HomeController : ControllerBase
{
    private readonly IMcpServerService _mcpServerService;
    private readonly IGptService _gptService;
    private readonly IChatSessionService _chatSessionService;
    private readonly IToolExecutionService _toolExecutionService;
    private readonly IQuestionEnhancementService _questionEnhancementService;
    private readonly ILogger<HomeController> _logger;

    public HomeController(
        IMcpServerService mcpServerService,
        IGptService gptService,
        IChatSessionService chatSessionService,
        IToolExecutionService toolExecutionService,
        IQuestionEnhancementService questionEnhancementService,
        ILogger<HomeController> logger)
    {
        _mcpServerService = mcpServerService;
        _gptService = gptService;
        _chatSessionService = chatSessionService;
        _toolExecutionService = toolExecutionService;
        _questionEnhancementService = questionEnhancementService;
        _logger = logger;
    }

    [HttpPost("ask")]
    public async Task<ActionResult<AskResponse>> Ask([FromBody] AskRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Question))
            {
                return BadRequest(new AskResponse { Error = "Question is required" });
            }

            // Generate or use existing chat ID
            var session = _chatSessionService.GetOrCreateSession(request.ChatId);

            // Add user message to session
            _chatSessionService.AddMessage(session.Id, new ChatMessage
            {
                Role = "user",
                Content = request.Question,
                Timestamp = DateTime.UtcNow
            });

            // Merge user context with session context
            var combinedContext = new Dictionary<string, object>(session.Context);
            foreach (var kvp in request.UserContext)
            {
                combinedContext[kvp.Key] = kvp.Value;
            }

            // Check for simple greetings or casual conversation
            if (_questionEnhancementService.IsSimpleGreeting(request.Question))
            {
                var greetingResponse = _questionEnhancementService.GetGreetingResponse();

                _chatSessionService.AddMessage(session.Id, new ChatMessage
                {
                    Role = "assistant",
                    Content = greetingResponse,
                    Timestamp = DateTime.UtcNow
                });

                return Ok(new AskResponse
                {
                    ChatId = session.Id,
                    Question = request.Question,
                    Response = greetingResponse,
                    Approach = "simple-greeting"
                });
            }

            var tools = await _mcpServerService.FetchAvailableToolsAsync();

            if (request.UseMultiStep)
            {
                // Use multi-step planning approach
                var result = await _toolExecutionService.ExecuteMultiStepPlanAsync(
                    request.Question, tools, 5, session.Id, combinedContext);

                if (result.Success)
                {
                    // Add assistant response to session
                    _chatSessionService.AddMessage(session.Id, new ChatMessage
                    {
                        Role = "assistant",
                        Content = result.Answer ?? "",
                        Timestamp = DateTime.UtcNow
                    });

                    return Ok(new AskResponse
                    {
                        ChatId = session.Id,
                        Question = request.Question,
                        Response = result.Answer,
                        ExecutionLog = result.ExecutionLog,
                        TotalSteps = result.TotalSteps,
                        Approach = "multi-step"
                    });
                }
                else if (result.NeedsUserInput)
                {
                    // Store current state in session for continuation
                    _chatSessionService.UpdateContext(session.Id, new Dictionary<string, object>
                    {
                        ["pendingExecution"] = new
                        {
                            executionLog = result.ExecutionLog,
                            totalSteps = result.TotalSteps,
                            originalQuestion = request.Question
                        }
                    });

                    return Ok(new AskResponse
                    {
                        ChatId = session.Id,
                        Question = request.Question,
                        NeedsUserInput = true,
                        UserQuestion = result.Question,
                        MissingParams = result.MissingParams,
                        ExecutionLog = result.ExecutionLog,
                        TotalSteps = result.TotalSteps,
                        Approach = "multi-step"
                    });
                }
                else
                {
                    // Add assistant response to session
                    var errorMessage = $"I couldn't complete this task. {result.Reason}";
                    _chatSessionService.AddMessage(session.Id, new ChatMessage
                    {
                        Role = "assistant",
                        Content = errorMessage,
                        Timestamp = DateTime.UtcNow
                    });

                    return Ok(new AskResponse
                    {
                        ChatId = session.Id,
                        Question = request.Question,
                        Response = errorMessage,
                        ExecutionLog = result.ExecutionLog,
                        TotalSteps = result.TotalSteps,
                        Approach = "multi-step"
                    });
                }
            }

            // Fallback to single-step approach - simplified version
            var directResponse = await _gptService.CallGptAsync(new List<GptMessage>
            {
                new() { Role = "user", Content = $"Please respond to this question: \"{request.Question}\"" }
            });

            return Ok(new AskResponse
            {
                ChatId = session.Id,
                Question = request.Question,
                Response = directResponse,
                ToolsUsed = new List<ToolCall>(),
                ToolResults = new List<ToolResult>(),
                Approach = "single-step"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing request for question: {Question}", request.Question);
            return StatusCode(500, new AskResponse
            {
                Error = ex.Message,
                Question = request.Question
            });
        }
    }

    [HttpGet("chat/{chatId}")]
    public ActionResult<ChatSession> GetChat(string chatId)
    {
        var session = _chatSessionService.GetSession(chatId);
        if (session == null)
        {
            return NotFound(new { error = "Chat session not found" });
        }

        return Ok(session);
    }

    [HttpGet("health")]
    public ActionResult<HealthResponse> Health()
    {
        return Ok(new HealthResponse
        {
            Status = "healthy",
            McpServerUrl = HttpContext.RequestServices.GetRequiredService<IConfiguration>()["McpServerUrl"] ?? "http://localhost:3001",
            Timestamp = DateTime.UtcNow
        });
    }
}
