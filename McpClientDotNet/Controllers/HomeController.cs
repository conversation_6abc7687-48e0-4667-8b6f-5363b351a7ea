using Microsoft.AspNetCore.Mvc;
using McpClientDotNet.Models;
using McpClientDotNet.Services;

namespace McpClientDotNet.Controllers;

[ApiController]
[Route("")]
public class HomeController : ControllerBase
{
    private readonly IMcpServerService _mcpServerService;
    private readonly IGptService _gptService;
    private readonly IChatSessionService _chatSessionService;
    private readonly IToolExecutionService _toolExecutionService;
    private readonly IQuestionEnhancementService _questionEnhancementService;
    private readonly ILogger<HomeController> _logger;

    public HomeController(
        IMcpServerService mcpServerService,
        IGptService gptService,
        IChatSessionService chatSessionService,
        IToolExecutionService toolExecutionService,
        IQuestionEnhancementService questionEnhancementService,
        ILogger<HomeController> logger)
    {
        _mcpServerService = mcpServerService;
        _gptService = gptService;
        _chatSessionService = chatSessionService;
        _toolExecutionService = toolExecutionService;
        _questionEnhancementService = questionEnhancementService;
        _logger = logger;
    }

    [HttpPost("ask")]
    public async Task<ActionResult<AskResponse>> Ask([FromBody] AskRequest request)
    {
        // Delegate to the MCP controller
        var mcpController = new McpController(
            _mcpServerService,
            _gptService,
            _chatSessionService,
            _toolExecutionService,
            _questionEnhancementService,
            _logger);

        mcpController.ControllerContext = ControllerContext;
        return await mcpController.Ask(request);
    }

    [HttpGet("chat/{chatId}")]
    public ActionResult<ChatSession> GetChat(string chatId)
    {
        var session = _chatSessionService.GetSession(chatId);
        if (session == null)
        {
            return NotFound(new { error = "Chat session not found" });
        }

        return Ok(session);
    }

    [HttpGet("health")]
    public ActionResult<HealthResponse> Health()
    {
        return Ok(new HealthResponse
        {
            Status = "healthy",
            McpServerUrl = HttpContext.RequestServices.GetRequiredService<IConfiguration>()["McpServerUrl"] ?? "http://localhost:3001",
            Timestamp = DateTime.UtcNow
        });
    }
}
