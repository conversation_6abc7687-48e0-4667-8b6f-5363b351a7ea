const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

const toolsRegistry = new Map();

function registerTool(name, description, params, handler) {
  toolsRegistry.set(name, {
    name,
    description,
    params,
    handler
  });
}

app.get('/tools', (req, res) => {
  const tools = Array.from(toolsRegistry.values()).map(tool => ({
    name: tool.name,
    description: tool.description,
    params: tool.params
  }));
  
  res.json({ tools });
});

app.post('/tool', async (req, res) => {
  try {
    const { toolName, params = {} } = req.body;
    
    if (!toolsRegistry.has(toolName)) {
      return res.status(404).json({ 
        error: `Tool '${toolName}' not found`,
        availableTools: Array.from(toolsRegistry.keys())
      });
    }
    
    const tool = toolsRegistry.get(toolName);
    
    const requiredParams = tool.params.filter(p => p.required);
    for (const param of requiredParams) {
      if (!(param.name in params)) {
        return res.status(400).json({
          error: `Missing required parameter: ${param.name}`,
          description: param.description
        });
      }
    }
    
    const result = await tool.handler(params);
    res.json({ result, toolName, params });
    
  } catch (error) {
    res.status(500).json({ 
      error: error.message,
      toolName: req.body.toolName 
    });
  }
});

registerTool(
  'getTime',
  'Get current date and time',
  [],
  async () => {
    return {
      timestamp: Date.now(),
      date: new Date().toISOString(),
      formatted: new Date().toLocaleString()
    };
  }
);





registerTool(
  'calculateDaysSince',
  'Calculate days between a date and now',
  [
    { name: 'date', required: true, description: 'Date to calculate from (ISO string or timestamp)' }
  ],
  async (params) => {
    const { date } = params;
    const targetDate = new Date(date);
    const now = new Date();

    if (isNaN(targetDate.getTime())) {
      throw new Error('Invalid date format');
    }

    const diffTime = Math.abs(now - targetDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return {
      targetDate: targetDate.toISOString(),
      currentDate: now.toISOString(),
      daysDifference: diffDays,
      wasInPast: targetDate < now
    };
  }
);







registerTool(
  'calculateDaysFromCurrentMonth',
  'Calculate days between a day in the current month and today',
  [
    { name: 'day', required: true, description: 'Day of the current month (1-31)' }
  ],
  async (params) => {
    const { day } = params;
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    const targetDate = new Date(currentYear, currentMonth, parseInt(day));

    // If the target day hasn't occurred this month yet, it refers to last month
    if (targetDate > now) {
      targetDate.setMonth(currentMonth - 1);
    }

    const diffTime = Math.abs(now - targetDate);
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    return {
      targetDate: targetDate.toISOString(),
      currentDate: now.toISOString(),
      daysDifference: diffDays,
      targetDay: day,
      targetMonth: targetDate.toLocaleString('default', { month: 'long' }),
      targetYear: targetDate.getFullYear()
    };
  }
);





// Autodesk Vault API Tools
const axios = require('axios');

// Helper function for file type descriptions
function getFileTypeDescription(extension) {
  const typeMap = {
    'iam': 'Inventor Assemblies',
    'ipt': 'Inventor Parts',
    'idw': 'Inventor Drawings',
    'dwg': 'AutoCAD Drawings',
    'pdf': 'PDF Documents',
    'xlsx': 'Excel Spreadsheets',
    'txt': 'Text Files',
    'docx': 'Word Documents',
    'pptx': 'PowerPoint Presentations'
  };
  return typeMap[extension] || `${extension.toUpperCase()} Files`;
}

// Helper function for Vault API requests
async function makeVaultRequest(endpoint, method = 'GET', data = null, accessToken = null, vaultServer = null) {
  if (!accessToken) {
    throw new Error('Access token is required for Vault API calls');
  }

  if (!vaultServer) {
    throw new Error('Vault server URL is required');
  }

  // Ensure proper base URL format for Vault API
  const baseUrl = vaultServer.endsWith('/') ? vaultServer.slice(0, -1) : vaultServer;
  const vaultApiBase = `${baseUrl}/AutodeskDM/Services/api/vault/v2`;
  const url = `${vaultApiBase}${endpoint}`;

  const config = {
    method,
    url,
    headers: {
      'Authorization': `Bearer ${accessToken}`,
      'Accept': 'application/json',
      'Content-Type': 'application/json'
    }
  };

  if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    config.data = data;
  }

  try {
    const response = await axios(config);
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`Vault API Error: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
    }
    throw new Error(`Vault API Request Failed: ${error.message}`);
  }
}







registerTool(
  'vaultGetFiles',
  'Get file versions from Autodesk Vault with optional filtering and search',
  [
    { name: 'accessToken', required: true, description: 'OAuth access token from Autodesk Platform Services' },
    { name: 'vaultServer', required: true, description: 'Vault server URL (e.g., https://your-vault-server.com)' },
    { name: 'vaultId', required: true, description: 'Vault ID to search in' },
    { name: 'searchTerm', required: false, description: 'Search term to filter files by name or properties' },
    { name: 'limit', required: false, description: 'Maximum number of file versions to return (default: 50)' }
  ],
  async (params) => {
    const { accessToken, vaultServer, vaultId, searchTerm, limit = 50 } = params;

    let endpoint = `/vaults/${vaultId}/file-versions`;
    const queryParams = [];

    if (searchTerm) queryParams.push(`q=${encodeURIComponent(searchTerm)}`);
    if (limit) queryParams.push(`limit=${limit}`);

    if (queryParams.length > 0) {
      endpoint += '?' + queryParams.join('&');
    }

    const result = await makeVaultRequest(endpoint, 'GET', null, accessToken, vaultServer);

    // Format the results for better presentation
    const files = result.results || [];
    const totalResults = result.pagination ? result.pagination.totalResults : files.length;

    if (files.length === 0) {
      return {
        message: "No files found in the vault.",
        pagination: result.pagination || {},
        fileVersions: [],
        included: result.included || {},
        searchTerm,
        vaultId,
        count: 0,
        totalResults: 0
      };
    }

    // Group files by type for better organization
    const filesByType = {};
    files.forEach(file => {
      const extension = file.name.split('.').pop().toLowerCase();
      if (!filesByType[extension]) {
        filesByType[extension] = [];
      }
      filesByType[extension].push(file);
    });

    // Create formatted output
    let formattedOutput = `Your Vault Files (${files.length} of ${totalResults} total files)\n\n`;

    // Sort file types for consistent display
    const sortedTypes = Object.keys(filesByType).sort();

    sortedTypes.forEach(type => {
      const typeFiles = filesByType[type];
      const typeDescription = getFileTypeDescription(type);

      formattedOutput += `${typeDescription} (.${type}) - ${typeFiles.length} files:\n`;

      typeFiles.forEach((file, index) => {
        const status = file.isCheckedOut ? 'Checked out' : 'Available';
        const checkoutInfo = file.isCheckedOut ? ` by ${file.checkoutUserName || 'Unknown'}` : '';
        formattedOutput += `  ${index + 1}. ${file.name} (v${file.version}) - ${status}${checkoutInfo}\n`;
      });

      formattedOutput += '\n';
    });

    if (totalResults > files.length) {
      formattedOutput += `Showing first ${files.length} files. Total files in vault: ${totalResults}`;
    }

    return {
      message: formattedOutput,
      pagination: result.pagination || {},
      fileVersions: files,
      included: result.included || {},
      searchTerm,
      vaultId,
      count: files.length,
      totalResults: totalResults
    };

  }
);



app.listen(PORT, () => {
  console.log(`MCP Server running on port ${PORT}`);
  console.log(`Available endpoints:`);
  console.log(`  GET  /tools - List all available tools`);
  console.log(`  POST /tool  - Execute a specific tool`);
  console.log(`\nRegistered tools: ${Array.from(toolsRegistry.keys()).join(', ')}`);
});
