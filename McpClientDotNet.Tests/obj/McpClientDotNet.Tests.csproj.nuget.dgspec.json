{"format": 1, "restore": {"/Users/<USER>/mcp_vault/McpClientDotNet.Tests/McpClientDotNet.Tests.csproj": {}}, "projects": {"/Users/<USER>/mcp_vault/McpClientDotNet.Tests/McpClientDotNet.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/mcp_vault/McpClientDotNet.Tests/McpClientDotNet.Tests.csproj", "projectName": "McpClientDotNet.Tests", "projectPath": "/Users/<USER>/mcp_vault/McpClientDotNet.Tests/McpClientDotNet.Tests.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/mcp_vault/McpClientDotNet.Tests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/mcp_vault/McpClientDotNet/McpClientDotNet.csproj": {"projectPath": "/Users/<USER>/mcp_vault/McpClientDotNet/McpClientDotNet.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Mvc.Testing": {"target": "Package", "version": "[8.0.11, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.5.3, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.5.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/mcp_vault/McpClientDotNet/McpClientDotNet.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/mcp_vault/McpClientDotNet/McpClientDotNet.csproj", "projectName": "McpClientDotNet", "projectPath": "/Users/<USER>/mcp_vault/McpClientDotNet/McpClientDotNet.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/mcp_vault/McpClientDotNet/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Cors": {"target": "Package", "version": "[2.3.0, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[8.0.14, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.6, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.407/PortableRuntimeIdentifierGraph.json"}}}}}